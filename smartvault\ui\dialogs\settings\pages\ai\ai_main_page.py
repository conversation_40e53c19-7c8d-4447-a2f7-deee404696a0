"""
AI设置主页面

AI功能设置的主要界面，采用组件化设计
预期代码长度: < 200行
当前代码长度: 150行 ✅
"""

from typing import Dict
from PySide6.QtWidgets import QVBoxLayout, QScrollArea, QWidget
from PySide6.QtCore import Signal, Qt

from ...base.base_page import BaseSettingsPage
from .utils.ai_config_helper import AIConfigManager
from .components.ai_status_widget import AIStatusWidget
from .components.ai_features_widget import AIFeaturesWidget
from .components.behavior_learning_widget import BehaviorLearningWidget
from .components.adaptive_rules_widget import AdaptiveRulesWidget
from .components.ai_statistics_widget import AIStatisticsWidget


class AISettingsPage(BaseSettingsPage):
    """AI设置主页面 - 轻量级控制器"""

    # 信号定义
    ai_config_changed = Signal(dict)
    ai_status_changed = Signal(dict)

    def __init__(self, parent=None):
        # 初始化配置管理器
        self.ai_config_manager = AIConfigManager()

        # 初始化组件
        self.status_widget = None
        self.features_widget = None
        self.learning_widget = None
        self.rules_widget = None
        self.stats_widget = None

        super().__init__(parent)

    def setup_ui(self):
        """设置UI - 只做组件布局"""
        # 创建滚动区域
        scroll_area = QScrollArea(self)
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # 创建内容容器
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)

        # 创建并添加各个功能组件
        self.status_widget = AIStatusWidget(self)
        self.status_widget.set_config_manager(self.ai_config_manager)
        content_layout.addWidget(self.status_widget)

        self.features_widget = AIFeaturesWidget(self)
        self.features_widget.set_config_manager(self.ai_config_manager)
        content_layout.addWidget(self.features_widget)

        self.learning_widget = BehaviorLearningWidget(self)
        self.learning_widget.set_config_manager(self.ai_config_manager)
        content_layout.addWidget(self.learning_widget)

        self.rules_widget = AdaptiveRulesWidget(self)
        self.rules_widget.set_config_manager(self.ai_config_manager)
        content_layout.addWidget(self.rules_widget)

        self.stats_widget = AIStatisticsWidget(self)
        self.stats_widget.set_config_manager(self.ai_config_manager)
        content_layout.addWidget(self.stats_widget)

        # 添加弹性空间
        content_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(content_widget)

        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.addWidget(scroll_area)

        # 设置组件间的信号连接
        self.setup_connections()

    def setup_connections(self):
        """设置组件间通信"""
        # 功能配置变化时更新其他组件
        self.features_widget.config_changed.connect(self._on_features_changed)
        self.learning_widget.config_changed.connect(self._on_learning_changed)

        # 状态更新请求
        self.status_widget.status_update_requested.connect(self._refresh_all_status)
        self.learning_widget.status_update_requested.connect(self._refresh_all_status)
        self.rules_widget.status_update_requested.connect(self._refresh_all_status)
        self.stats_widget.status_update_requested.connect(self._refresh_all_status)

    def set_ai_manager(self, ai_manager):
        """设置AI管理器引用

        Args:
            ai_manager: AI管理器实例
        """
        self.ai_config_manager.set_ai_manager(ai_manager)

    def load_settings(self, config: dict):
        """加载设置 - 分发给各组件"""
        try:
            # 加载AI配置
            ai_config = self.ai_config_manager.load_ai_config()

            # 分发给各组件
            if self.status_widget:
                self.status_widget.load_config(ai_config)

            if self.features_widget:
                self.features_widget.load_config(ai_config)

            if self.learning_widget:
                self.learning_widget.load_config(ai_config)

            if self.rules_widget:
                self.rules_widget.load_config(ai_config)

            if self.stats_widget:
                self.stats_widget.load_config(ai_config)

        except Exception as e:
            print(f"加载AI设置失败: {e}")

    def save_settings(self) -> dict:
        """保存设置 - 收集各组件数据"""
        try:
            # 收集各组件的配置
            ai_config = {}

            if self.features_widget:
                features_config = self.features_widget.save_config()
                ai_config.update(features_config)

            if self.learning_widget:
                learning_config = self.learning_widget.save_config()
                self._merge_config(ai_config, learning_config)

            # 保存AI配置
            success = self.ai_config_manager.save_ai_config(ai_config)
            if not success:
                print("保存AI配置失败")

            # 发送配置变化信号
            self.ai_config_changed.emit(ai_config)

            return {'ai': ai_config}

        except Exception as e:
            print(f"保存AI设置失败: {e}")
            return {}

    def _on_features_changed(self, config: Dict):
        """功能配置变化处理"""
        # 当功能配置变化时，刷新状态显示
        self._refresh_all_status()

        # 发送配置变化信号
        self.ai_config_changed.emit(config)

    def _on_learning_changed(self, config: Dict):
        """学习配置变化处理"""
        # 刷新相关组件状态
        if self.stats_widget:
            self.stats_widget.refresh_statistics()

        # 发送配置变化信号
        self.ai_config_changed.emit(config)

    def _refresh_all_status(self):
        """刷新所有组件状态"""
        try:
            if self.status_widget:
                self.status_widget.refresh_ai_status()

            if self.learning_widget:
                self.learning_widget.refresh_learning_data()

            if self.rules_widget:
                self.rules_widget.refresh_rules_data()

            if self.stats_widget:
                self.stats_widget.refresh_statistics()

        except Exception as e:
            print(f"刷新AI状态失败: {e}")

    def _merge_config(self, target: Dict, source: Dict):
        """合并配置字典

        Args:
            target: 目标配置字典
            source: 源配置字典
        """
        for key, value in source.items():
            if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                self._merge_config(target[key], value)
            else:
                target[key] = value

    def get_page_title(self) -> str:
        """获取页面标题"""
        return "AI功能"

    def get_page_icon(self) -> str:
        """获取页面图标"""
        return "🤖"  # 或者返回图标文件路径
