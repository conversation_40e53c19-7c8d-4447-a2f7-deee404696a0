"""
设置对话框 - 重构后的模块化版本
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QDialogButtonBox, QTabWidget, QMessageBox
)
from PySide6.QtCore import Qt, Signal
from smartvault.utils.config import load_config, save_config
from smartvault.data.file_system import FileSystem
from smartvault.services.file_monitor_service import FileMonitorService

# 导入所有设置页面
from .settings.pages import (
    UISettingsPage, SearchSettingsPage, AdvancedSettingsPage,
    MonitorSettingsPage, AutoTagSettingsPage, LibrarySettingsPage,
    ClipboardSettingsPage, BackupSettingsPage
)

# 导入监控配置对话框（为了兼容性）
from .monitor_config_dialog import MonitorConfigDialog


class SettingsDialog(QDialog):
    """设置对话框 - 模块化版本"""

    # 自定义信号
    library_changed = Signal(str)  # 文件库路径变更信号

    def __init__(self, parent=None):
        """初始化设置对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.config = load_config()
        self.file_system = FileSystem(self.config.get("library_path", ""))
        self.monitor_service = None
        self.pages = {}

        self.init_ui()

    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(800, 600)

        layout = QVBoxLayout(self)

        # 创建标签页控件
        self.tab_widget = QTabWidget()

        # 创建所有设置页面
        self.create_pages()

        layout.addWidget(self.tab_widget)

        # 创建按钮
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel,
            Qt.Horizontal, self
        )
        button_box.accepted.connect(self.on_accept)
        button_box.rejected.connect(self.reject)

        # 设置中文按钮文本
        ok_button = button_box.button(QDialogButtonBox.Ok)
        cancel_button = button_box.button(QDialogButtonBox.Cancel)
        if ok_button:
            ok_button.setText("确定")
        if cancel_button:
            cancel_button.setText("取消")

        layout.addWidget(button_box)

    def switch_to_page(self, page_key):
        """切换到指定页面

        Args:
            page_key: 页面键名，如 'backup', 'library', 'monitor' 等
        """
        try:
            # 查找对应的页面索引
            page_titles = {
                'library': '文件库设置',
                'monitor': '文件监控',
                'clipboard': '剪贴板监控',
                'ui': '界面设置',
                'search': '搜索设置',
                'auto_tag': '自动标签',
                'backup': '备份设置',
                'advanced': '高级设置'
            }

            target_title = page_titles.get(page_key)
            if target_title:
                for i in range(self.tab_widget.count()):
                    if self.tab_widget.tabText(i) == target_title:
                        self.tab_widget.setCurrentIndex(i)
                        break
        except Exception as e:
            print(f"切换到页面 {page_key} 失败: {e}")

    def create_pages(self):
        """创建所有设置页面"""
        # 创建页面实例
        self.pages['library'] = LibrarySettingsPage()
        self.pages['monitor'] = MonitorSettingsPage()
        self.pages['clipboard'] = ClipboardSettingsPage()
        self.pages['ui'] = UISettingsPage()
        self.pages['search'] = SearchSettingsPage()
        self.pages['auto_tag'] = AutoTagSettingsPage()
        self.pages['backup'] = BackupSettingsPage()
        self.pages['advanced'] = AdvancedSettingsPage()

        # 设置依赖项
        self.pages['library'].set_file_system(self.file_system)
        if self.monitor_service:
            self.pages['monitor'].set_monitor_service(self.monitor_service)

        # 连接信号
        self.pages['library'].library_changed.connect(self.library_changed)

        # 添加到标签页
        for page_key, page_instance in self.pages.items():
            self.tab_widget.addTab(page_instance, page_instance.get_page_title())
            # 加载设置
            page_instance.load_settings(self.config)

    def set_monitor_service(self, monitor_service):
        """设置监控服务实例

        Args:
            monitor_service: FileMonitorService实例
        """
        self.monitor_service = monitor_service
        if 'monitor' in self.pages:
            self.pages['monitor'].set_monitor_service(monitor_service)
            # 重新刷新监控列表，因为现在有了服务实例
            self.pages['monitor'].refresh_monitor_list()

    def on_accept(self):
        """确定按钮点击事件"""
        try:
            # 验证所有页面设置
            for page_key, page_instance in self.pages.items():
                is_valid, error_msg = page_instance.validate_settings()
                if not is_valid:
                    QMessageBox.warning(self, "设置错误", f"{page_instance.get_page_title()}: {error_msg}")
                    return

            # 收集所有页面的设置
            new_config = self.config.copy()

            for page_key, page_instance in self.pages.items():
                page_settings = page_instance.save_settings()

                # 根据页面类型正确合并设置
                if isinstance(page_settings, dict):
                    if page_key == 'ui':
                        # UI页面设置合并到ui节点
                        if 'ui' not in new_config:
                            new_config['ui'] = {}
                        new_config['ui'].update(page_settings)
                    elif page_key == 'search':
                        # 搜索页面设置合并到search节点
                        if 'search' not in new_config:
                            new_config['search'] = {}
                        new_config['search'].update(page_settings)
                    elif page_key == 'advanced':
                        # 高级页面设置合并到advanced节点
                        if 'advanced' not in new_config:
                            new_config['advanced'] = {}
                        new_config['advanced'].update(page_settings)
                    elif page_key == 'monitor':
                        # 监控页面设置合并到monitor节点
                        if 'monitor' not in new_config:
                            new_config['monitor'] = {}
                        new_config['monitor'].update(page_settings)
                    elif page_key == 'auto_tag':
                        # 自动标签页面设置合并到auto_tags节点
                        if 'auto_tags' not in new_config:
                            new_config['auto_tags'] = {}
                        new_config['auto_tags'].update(page_settings)
                    elif page_key == 'clipboard':
                        # 剪贴板页面设置合并到clipboard节点
                        if 'clipboard' not in new_config:
                            new_config['clipboard'] = {}
                        new_config['clipboard'].update(page_settings)
                    elif page_key == 'backup':
                        # 备份页面设置合并到backup节点
                        if 'backup' not in new_config:
                            new_config['backup'] = {}
                        new_config['backup'].update(page_settings)
                    elif page_key == 'library':
                        # 文件库页面设置直接合并到根节点
                        new_config.update(page_settings)

            # 保存配置
            save_config(new_config)

            # 应用新配置
            self.apply_new_config(new_config)

            # 直接关闭对话框，不显示成功消息
            self.accept()

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
            import traceback
            traceback.print_exc()

    def apply_new_config(self, new_config):
        """应用新配置

        Args:
            new_config: 新的配置字典
        """
        try:
            # 检查文件库路径是否变更
            old_library_path = self.config.get("library_path", "")
            new_library_path = new_config.get("library_path", "")

            if old_library_path != new_library_path:
                self.library_changed.emit(new_library_path)

            # 更新内部配置
            self.config = new_config

        except Exception as e:
            print(f"应用配置失败: {e}")

    def get_current_config(self):
        """获取当前配置

        Returns:
            dict: 当前配置字典
        """
        return self.config.copy()
