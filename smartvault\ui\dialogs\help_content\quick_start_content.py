# -*- coding: utf-8 -*-
"""
SmartVault 快速入门内容
"""

QUICK_START_CONTENT = {
    "新手指南": """
# SmartVault 新手指南

> **5分钟快速上手SmartVault智能文件管理系统**

## 🎯 什么是SmartVault？

SmartVault是一个智能文件管理系统，就像给您的电脑配备了一个聪明的文件管家。它可以：

- 📁 **整理文件**：把散落在电脑各处的文件集中管理
- 🏷️ **智能分类**：用标签让文件井井有条
- 🔍 **快速查找**：几秒钟找到任何需要的文件
- 🤖 **自动处理**：自动整理新文件，省心省力

## 🚀 第一次使用（5分钟教程）

### 步骤1：启动软件（30秒）
1. 双击桌面上的SmartVault图标
2. 等待软件启动完成
3. 您会看到一个清爽的主界面

### 步骤2：添加第一个文件（1分钟）
1. 点击工具栏上的 **"添加文件"** 按钮
2. 选择一个您常用的文件（比如一张照片）
3. 点击 **"打开"**
4. 🎉 恭喜！您的第一个文件已经添加到智能文件库了

### 步骤3：给文件打标签（1分钟）
1. 在文件列表中找到刚才添加的文件
2. 右键点击文件，选择 **"编辑标签"**
3. 输入一个描述性的标签，比如"重要照片"
4. 点击 **"确定"**
5. 现在您可以通过标签快速找到这个文件了

### 步骤4：尝试搜索（30秒）
1. 在顶部搜索框中输入刚才的标签"重要照片"
2. 按回车键
3. 看！您的文件立即出现在搜索结果中

### 步骤5：添加文件夹（2分钟）
1. 点击 **"添加文件夹"** 按钮
2. 选择一个包含多个文件的文件夹
3. 选择添加方式：
   - **复制到智能文件库**：在智能文件库中创建副本（推荐）
   - **移动到智能文件库**：将文件移动到智能文件库
   - **链接到智能文件库**：保持原位置，只在库中创建引用
4. 点击 **"确定"**
5. 等待处理完成，您会看到所有文件都出现在列表中

## 🎨 界面介绍

### 主界面布局
```
┌─────────────────────────────────────────────────────┐
│  [菜单栏] [工具栏]                    [搜索框]      │
├─────────────────────────────────────────────────────┤
│ [导航面板]  │           [文件列表]                  │
│             │                                       │
│ 📁 所有文件  │  📄 文件1.txt    🏷️ 工作文档         │
│ 📁 最近添加  │  📷 照片1.jpg    🏷️ 家庭照片         │
│ 📁 图片     │  📊 报告.xlsx    🏷️ 重要文件         │
│ 📁 文档     │                                       │
│ 📁 视频     │                                       │
│             │                                       │
├─────────────────────────────────────────────────────┤
│ [状态栏] 文件总数：1,234 | 已选择：3 | 存储空间：2.5GB │
└─────────────────────────────────────────────────────┘
```

### 主要按钮说明
- 🔍 **搜索框**：输入关键词快速查找文件
- ➕ **添加文件**：将单个文件添加到智能文件库
- 📁 **添加文件夹**：批量添加整个文件夹
- 🏷️ **标签管理**：管理所有标签
- ⚙️ **设置**：配置软件选项

## 🏷️ 标签使用技巧

### 什么是标签？
标签就像给文件贴的小纸条，用简单的词语描述文件的内容或用途。

### 标签命名建议
- **用途标签**：工作、学习、娱乐、重要
- **项目标签**：项目A、年度报告、家庭旅行
- **状态标签**：待处理、已完成、需要审核
- **类型标签**：合同、发票、照片、视频

### 标签使用示例
```
📄 工作报告.docx
   🏷️ 工作 | 重要 | 2024年度 | 待审核

📷 生日聚会.jpg  
   🏷️ 家庭 | 照片 | 2024年 | 生日

📊 销售数据.xlsx
   🏷️ 工作 | 数据分析 | 月度报告
```

## 🔍 搜索小技巧

### 基本搜索
- 直接输入文件名或标签
- 支持中文、英文、数字

### 搜索技巧
- **多关键词**：用空格分隔，如"工作 报告"
- **精确匹配**：用引号包围，如"年度总结"
- **标签搜索**：直接输入标签名

### 高级搜索
点击搜索框旁的 **"高级"** 按钮，可以：
- 按文件类型筛选
- 按时间范围筛选
- 按文件大小筛选

## 🤖 自动化功能

### 文件夹监控
让SmartVault自动整理特定文件夹的新文件：

1. 进入 **设置 → 监控页面**
2. 点击 **"添加监控文件夹"**
3. 选择要监控的文件夹（如下载文件夹）
4. 设置自动标签规则
5. 开启监控

现在，每当该文件夹有新文件时，SmartVault会自动添加到智能文件库并打上标签！

### 剪贴板监控
智能处理您复制的文件：

1. 进入 **设置 → 剪贴板页面**
2. 开启 **"剪贴板监控"**
3. 现在当您复制文件时，SmartVault会：
   - 检查是否已存在相同文件
   - 显示浮动提示窗口
   - 提供快速添加选项

## ⚠️ 重要提醒

### 文件安全
- SmartVault不会删除您的原始文件
- 建议定期备份智能文件库
- 重要操作前可以先创建备份

### 存储空间
- 选择"复制"方式会占用额外空间
- 选择"链接"方式不占用额外空间
- 可以在设置中查看存储使用情况

### 性能优化
- 文件数量很多时，搜索可能需要几秒钟
- 建议定期清理不需要的文件
- 合理使用标签可以提高查找效率

## 🎉 恭喜您！

现在您已经掌握了SmartVault的基本使用方法！

### 下一步建议：
1. 📖 阅读 **基本概念** 了解更多原理
2. 🔧 查看 **软件设置** 个性化配置
3. 🏷️ 学习 **标签系统** 的高级用法

**开始享受智能文件管理的便利吧！** ✨
""",

    "基本概念": """
# SmartVault 基本概念

> **理解SmartVault的核心概念，让您更好地使用智能文件管理系统**

## 🎯 核心概念

### 📚 智能文件库
智能文件库是SmartVault的核心，它不是简单的文件夹，而是一个智能的文件管理系统：

- **统一管理**：将分散在电脑各处的文件集中管理
- **智能索引**：为每个文件建立详细的索引信息
- **快速检索**：支持多种方式快速查找文件
- **自动整理**：根据规则自动分类和标记文件

### 🏷️ 标签系统
标签是SmartVault的灵魂，它让文件管理变得灵活而强大：

- **自由标记**：用任意词语为文件打标签
- **多维分类**：一个文件可以有多个标签
- **智能建议**：系统会根据文件内容推荐标签
- **快速筛选**：通过标签组合快速定位文件

### 📁 文件夹结构
SmartVault采用虚拟文件夹概念，不依赖物理文件夹结构：

- **中转文件夹**：新添加文件的临时存放区
- **智能分类**：按文件类型自动分类（图片、文档、视频等）
- **自定义视图**：根据标签、时间等创建自定义文件夹视图

## 📂 文件添加方式

### 🔗 链接到智能仓库
- **原理**：在智能文件库中创建文件引用，文件保持在原位置
- **优点**：不占用额外存储空间，文件保持原有组织结构
- **缺点**：如果原文件被移动或删除，链接会失效
- **适用场景**：管理大量文件，或需要保持原有文件结构

### 📋 复制到智能仓库
- **原理**：将文件复制到智能文件库的存储目录
- **优点**：文件安全性高，不受原文件变化影响
- **缺点**：占用额外存储空间
- **适用场景**：重要文件管理，需要长期保存的文件

### 🚚 移动到智能仓库
- **原理**：将文件从原位置移动到智能文件库
- **优点**：不占用额外空间，文件完全由SmartVault管理
- **缺点**：改变了文件的原有位置
- **适用场景**：整理散乱文件，完全托管给SmartVault

## 🔍 搜索与筛选

### 全文搜索
SmartVault支持多种搜索方式：

- **文件名搜索**：根据文件名查找
- **标签搜索**：根据标签查找
- **内容搜索**：搜索文件内容（支持文本文件）
- **元数据搜索**：根据文件大小、创建时间等查找

### 高级筛选
- **类型筛选**：按文件类型（图片、文档、视频等）筛选
- **时间筛选**：按添加时间、修改时间筛选
- **大小筛选**：按文件大小范围筛选
- **标签组合**：使用多个标签组合筛选

## 🤖 自动化功能

### 📁 文件夹监控
自动监控指定文件夹的变化：

- **实时监控**：检测新文件的添加
- **自动处理**：根据规则自动添加到智能文件库
- **智能标记**：根据文件类型和位置自动打标签
- **重复检测**：自动检测和处理重复文件

### 📋 剪贴板监控
智能处理复制粘贴操作：

- **文件监控**：检测复制的文件
- **重复提醒**：如果文件已存在，及时提醒
- **快速添加**：提供快速添加到智能文件库的选项
- **智能建议**：根据文件内容建议标签

## 💾 数据存储

### 数据库结构
SmartVault使用SQLite数据库存储文件信息：

- **文件表**：存储文件的基本信息和元数据
- **标签表**：管理所有标签信息
- **关联表**：维护文件和标签的关联关系
- **配置表**：存储用户设置和系统配置

### 文件存储
- **库文件**：复制和移动的文件存储在智能文件库目录
- **链接文件**：保持在原位置，数据库中记录路径
- **缩略图**：自动生成图片和视频的缩略图
- **索引文件**：为快速搜索建立的索引信息

## 🔒 安全与备份

### 数据安全
- **自动备份**：定期自动备份数据库
- **完整性检查**：定期检查文件完整性
- **错误恢复**：提供数据恢复功能
- **权限控制**：保护文件访问权限

### 备份策略
- **增量备份**：只备份变化的数据
- **完整备份**：定期创建完整备份
- **多版本保留**：保留多个备份版本
- **自动清理**：自动清理过期备份

## 🎨 用户界面

### 响应式设计
- **自适应布局**：根据窗口大小自动调整
- **主题支持**：支持明暗两种主题
- **个性化设置**：可自定义界面元素
- **快捷键支持**：提供丰富的快捷键操作

### 交互体验
- **拖拽支持**：支持拖拽添加文件
- **右键菜单**：提供丰富的右键操作
- **状态反馈**：及时显示操作状态和进度
- **错误提示**：友好的错误信息和解决建议

## 📈 性能优化

### 加载优化
- **分页加载**：大量文件时分页显示
- **延迟加载**：按需加载文件详细信息
- **缓存机制**：缓存常用数据提高响应速度
- **后台处理**：耗时操作在后台进行

### 存储优化
- **重复检测**：避免存储重复文件
- **压缩存储**：对适合的文件进行压缩
- **清理机制**：定期清理临时文件和缓存
- **空间监控**：监控存储空间使用情况

---

理解了这些基本概念，您就能更好地使用SmartVault的各种功能了！

**下一步建议**：查看 **文件管理** 了解具体的操作方法。
"""
}
