# -*- coding: utf-8 -*-
"""
SmartVault 设置与维护内容
"""

SETTINGS_CONTENT = {
    "软件设置": """
# 软件设置

## ⚙️ 基本设置

### 界面设置
- **主题选择**：明亮主题 / 深色主题
- **语言设置**：界面语言选择
- **字体大小**：调整界面字体大小
- **窗口布局**：自定义窗口布局

### 文件显示
- **缩略图大小**：调整文件缩略图大小
- **列表视图**：选择文件列表显示方式
- **排序方式**：默认文件排序方式
- **分页大小**：每页显示的文件数量

## 📁 存储设置

### 智能文件库位置
- 查看当前文件库位置
- 更改文件库存储位置
- 移动现有文件库
- 创建新的文件库

### 存储管理
- **存储空间监控**：监控磁盘空间使用
- **自动清理**：定期清理临时文件
- **压缩设置**：文件压缩选项
- **重复文件处理**：重复文件处理策略

## 🔍 搜索设置

### 搜索选项
- **搜索范围**：设置默认搜索范围
- **搜索历史**：保存搜索历史数量
- **实时搜索**：启用输入时实时搜索
- **搜索结果**：搜索结果显示数量

### 索引设置
- **文件内容索引**：是否索引文件内容
- **索引更新**：索引更新频率
- **索引重建**：手动重建搜索索引
- **索引优化**：优化搜索性能

## 🏷️ 标签设置

### 标签管理
- **自动标签**：启用自动标签建议
- **标签规则**：设置自动标签规则
- **标签清理**：清理未使用的标签
- **标签导入导出**：标签数据管理

### 标签显示
- **标签颜色**：设置标签显示颜色
- **标签排序**：标签显示排序方式
- **标签筛选**：标签筛选选项
- **标签统计**：显示标签使用统计

## 🔄 同步设置

### 自动同步
- **启用自动同步**：开启自动同步功能
- **同步频率**：设置同步检查频率
- **同步范围**：选择同步的内容范围
- **冲突处理**：同步冲突处理策略

### 手动同步
- **立即同步**：手动触发同步
- **同步状态**：查看同步状态
- **同步日志**：查看同步历史记录
- **同步设置重置**：重置同步设置
""",

    "备份恢复": """
# 备份恢复

## 💾 数据备份

### 自动备份
SmartVault提供自动备份功能，保护您的数据安全：
- **定期备份**：按设定频率自动备份
- **增量备份**：只备份变化的数据
- **多版本保留**：保留多个备份版本
- **自动清理**：自动清理过期备份

### 手动备份
1. 进入"设置" → "备份恢复"
2. 点击"立即备份"
3. 选择备份位置
4. 等待备份完成

### 备份内容
- **数据库文件**：包含所有文件信息和标签
- **配置文件**：软件设置和用户配置
- **缩略图缓存**：文件缩略图数据
- **用户文件**：复制到智能库的文件（可选）

## 🔄 数据恢复

### 从备份恢复
1. 进入"设置" → "备份恢复"
2. 点击"恢复数据"
3. 选择备份文件
4. 确认恢复操作
5. 重启软件完成恢复

### 恢复选项
- **完整恢复**：恢复所有数据和设置
- **部分恢复**：只恢复选定的数据
- **合并恢复**：将备份数据与现有数据合并
- **覆盖恢复**：完全替换现有数据

## 📤 数据导出

### 导出文件列表
- 导出为Excel格式
- 导出为CSV格式
- 导出为JSON格式
- 自定义导出字段

### 导出标签数据
- 导出标签列表
- 导出标签关联关系
- 导出标签使用统计
- 导出为多种格式

## 📥 数据导入

### 从其他系统导入
- 支持从文件管理软件导入
- 支持从表格文件导入
- 支持从其他SmartVault实例导入
- 自定义导入规则

### 导入选项
- **重复处理**：处理重复文件的策略
- **标签映射**：标签名称映射规则
- **路径处理**：文件路径处理方式
- **错误处理**：导入错误的处理方式

## 🔧 维护工具

### 数据库维护
- **完整性检查**：检查数据库完整性
- **索引重建**：重建数据库索引
- **数据库优化**：优化数据库性能
- **数据库修复**：修复损坏的数据库

### 文件维护
- **文件完整性检查**：检查文件是否存在
- **缩略图重建**：重新生成缩略图
- **临时文件清理**：清理临时文件
- **孤立文件清理**：清理无关联的文件

## ⚠️ 重要提醒

### 备份建议
- 定期创建完整备份
- 重要操作前先备份
- 将备份存储在不同位置
- 定期测试备份恢复

### 数据安全
- 备份文件加密保护
- 避免在不稳定环境下操作
- 保持足够的磁盘空间
- 及时更新软件版本
""",

    "常见问题": """
# 常见问题

## 🚀 安装和启动

### Q: 软件无法启动怎么办？
**A:** 请尝试以下解决方案：
1. 检查系统要求是否满足
2. 以管理员身份运行
3. 检查防病毒软件是否阻止
4. 重新安装软件

### Q: 启动时提示数据库错误？
**A:** 可能的解决方案：
1. 检查数据库文件是否损坏
2. 尝试从备份恢复
3. 重新初始化数据库
4. 联系技术支持

## 📁 文件管理

### Q: 添加文件后找不到文件？
**A:** 请检查：
1. 文件是否在中转文件夹中
2. 搜索功能是否正常
3. 文件是否被过滤隐藏
4. 检查文件添加日志

### Q: 链接文件显示"文件不存在"？
**A:** 可能原因：
1. 原文件被移动或删除
2. 磁盘驱动器号发生变化
3. 网络路径不可访问
4. 权限不足

### Q: 重复文件如何处理？
**A:** SmartVault提供多种处理方式：
1. 自动跳过重复文件
2. 自动重命名重复文件
3. 手动选择处理方式
4. 批量清理重复文件

## 🏷️ 标签和搜索

### Q: 标签无法添加或显示异常？
**A:** 请尝试：
1. 检查标签名称是否合法
2. 重启软件重新加载
3. 检查数据库完整性
4. 重建搜索索引

### Q: 搜索结果不准确或缺失？
**A:** 可能的解决方案：
1. 重建搜索索引
2. 检查搜索设置
3. 确认文件是否存在
4. 清理搜索缓存

## 🔧 性能问题

### Q: 软件运行缓慢怎么办？
**A:** 优化建议：
1. 清理临时文件和缓存
2. 优化数据库
3. 减少同时显示的文件数量
4. 关闭不必要的监控功能

### Q: 占用内存过多？
**A:** 可以尝试：
1. 重启软件释放内存
2. 减少缩略图缓存大小
3. 关闭文件内容索引
4. 升级系统内存

## 💾 备份和恢复

### Q: 备份文件过大怎么办？
**A:** 优化方案：
1. 选择增量备份
2. 排除不必要的文件
3. 压缩备份文件
4. 定期清理旧备份

### Q: 恢复备份后数据丢失？
**A:** 请检查：
1. 备份文件是否完整
2. 恢复操作是否正确
3. 是否选择了正确的恢复选项
4. 检查恢复日志

## 🔄 监控功能

### Q: 文件夹监控不工作？
**A:** 排查步骤：
1. 检查监控状态是否启用
2. 确认监控路径是否正确
3. 检查文件夹权限
4. 重启监控服务

### Q: 剪贴板监控无响应？
**A:** 可能的解决方案：
1. 重新启用剪贴板监控
2. 检查系统剪贴板权限
3. 重启软件
4. 检查防病毒软件设置

## 🆘 获取帮助

### 技术支持
如果以上解决方案都无法解决您的问题：
1. 查看软件日志文件
2. 记录详细的错误信息
3. 准备系统环境信息
4. 联系技术支持团队

### 反馈建议
我们欢迎您的反馈和建议：
- 功能改进建议
- 界面优化建议
- 性能优化建议
- 新功能需求

---

**提示**：大部分问题可以通过重启软件或重建索引解决。如果问题持续存在，请及时备份数据并联系技术支持。
"""
}
