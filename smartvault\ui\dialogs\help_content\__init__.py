# -*- coding: utf-8 -*-
"""
SmartVault 内嵌帮助内容模块

为了避免超长代码文件，帮助内容按功能模块分别存储
"""

from .welcome_content import WELCOME_CONTENT
from .quick_start_content import QUICK_START_CONTENT
from .core_features_content import CORE_FEATURES_CONTENT
from .advanced_features_content import ADVANCED_FEATURES_CONTENT
from .settings_content import SETTINGS_CONTENT

# 统一的帮助内容字典
HELP_CONTENT = {
    # 欢迎页面
    "welcome": WELCOME_CONTENT,
    
    # 快速入门
    "用户帮助-新手指南.md": QUICK_START_CONTENT["新手指南"],
    "用户帮助-基本概念.md": QUICK_START_CONTENT["基本概念"],
    
    # 核心功能
    "用户帮助-文件管理.md": CORE_FEATURES_CONTENT["文件管理"],
    "用户帮助-标签系统.md": CORE_FEATURES_CONTENT["标签系统"],
    "用户帮助-搜索功能.md": CORE_FEATURES_CONTENT["搜索功能"],
    
    # 高级功能
    "用户帮助-自动监控.md": ADVANCED_FEATURES_CONTENT["自动监控"],
    "用户帮助-剪贴板监控.md": ADVANCED_FEATURES_CONTENT["剪贴板监控"],
    "用户帮助-设备管理.md": ADVANCED_FEATURES_CONTENT["设备管理"],
    
    # 设置与维护
    "用户帮助-软件设置.md": SETTINGS_CONTENT["软件设置"],
    "用户帮助-备份恢复.md": SETTINGS_CONTENT["备份恢复"],
    "用户帮助-常见问题.md": SETTINGS_CONTENT["常见问题"],
}

__all__ = ['HELP_CONTENT']
