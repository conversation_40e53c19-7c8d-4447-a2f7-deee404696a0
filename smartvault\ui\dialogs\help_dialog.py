"""
SmartVault 帮助对话框
"""

import os
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTreeWidget, QTreeWidgetItem,
    QTextBrowser, QSplitter, QPushButton, QLineEdit, QLabel,
    QMessageBox
)
from PySide6.QtCore import Qt, QUrl
from PySide6.QtGui import QFont, QIcon, QDesktopServices


class HelpDialog(QDialog):
    """帮助对话框"""

    def __init__(self, parent=None):
        """初始化帮助对话框

        Args:
            parent: 父窗口
        """
        super().__init__(parent)
        self.setWindowTitle("SmartVault 用户帮助")
        self.setWindowIcon(QIcon("icons/help.png"))
        self.resize(1000, 700)

        # 帮助文档路径（保留作为备用）
        self.docs_path = os.path.join(os.path.dirname(__file__), "..", "..", "..", "docs")

        # 导入内嵌的帮助内容
        try:
            from smartvault.ui.dialogs.help_content import HELP_CONTENT
            self.embedded_help = HELP_CONTENT
        except ImportError:
            self.embedded_help = None

        # 帮助文档结构
        self.help_structure = {
            "🚀 快速入门": {
                "icon": "🚀",
                "items": {
                    "新手指南": "用户帮助-新手指南.md",
                    "基本概念": "用户帮助-基本概念.md"
                }
            },
            "📁 核心功能": {
                "icon": "📁",
                "items": {
                    "文件管理": "用户帮助-文件管理.md",
                    "标签系统": "用户帮助-标签系统.md",
                    "搜索功能": "用户帮助-搜索功能.md"
                }
            },
            "🔧 高级功能": {
                "icon": "🔧",
                "items": {
                    "自动监控": "用户帮助-自动监控.md",
                    "剪贴板监控": "用户帮助-剪贴板监控.md",
                    "设备管理": "用户帮助-设备管理.md"
                }
            },
            "⚙️ 设置与维护": {
                "icon": "⚙️",
                "items": {
                    "软件设置": "用户帮助-软件设置.md",
                    "备份恢复": "用户帮助-备份恢复.md",
                    "常见问题": "用户帮助-常见问题.md"
                }
            }
        }

        # 当前显示的文档
        self.current_doc = None

        # 初始化UI
        self.init_ui()

        # 加载默认页面
        self.load_welcome_page()

    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)

        # 顶部搜索栏
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("搜索帮助:"))

        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("输入关键词搜索帮助内容...")
        self.search_edit.textChanged.connect(self.on_search_text_changed)
        search_layout.addWidget(self.search_edit)

        search_button = QPushButton("搜索")
        search_button.clicked.connect(self.search_help)
        search_layout.addWidget(search_button)

        layout.addLayout(search_layout)

        # 主要内容区域
        splitter = QSplitter(Qt.Horizontal)

        # 左侧导航树
        self.tree_widget = QTreeWidget()
        self.tree_widget.setHeaderLabel("帮助目录")
        self.tree_widget.setMaximumWidth(300)
        self.tree_widget.itemClicked.connect(self.on_tree_item_clicked)
        self.populate_tree()
        splitter.addWidget(self.tree_widget)

        # 右侧内容显示
        self.content_browser = QTextBrowser()
        self.content_browser.setOpenExternalLinks(False)
        self.content_browser.anchorClicked.connect(self.on_link_clicked)

        # 设置字体
        font = QFont()
        font.setPointSize(10)
        self.content_browser.setFont(font)

        splitter.addWidget(self.content_browser)

        # 设置分割器比例
        splitter.setSizes([300, 700])
        layout.addWidget(splitter)

        # 底部按钮
        button_layout = QHBoxLayout()

        # 打开文档文件夹按钮
        open_docs_button = QPushButton("打开文档文件夹")
        open_docs_button.clicked.connect(self.open_docs_folder)
        button_layout.addWidget(open_docs_button)

        button_layout.addStretch()

        # 关闭按钮
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

    def populate_tree(self):
        """填充导航树"""
        # 添加欢迎页面
        welcome_item = QTreeWidgetItem(self.tree_widget)
        welcome_item.setText(0, "🏠 欢迎使用SmartVault")
        welcome_item.setData(0, Qt.UserRole, "welcome")

        # 添加帮助分类
        for category, info in self.help_structure.items():
            category_item = QTreeWidgetItem(self.tree_widget)
            category_item.setText(0, category)
            category_item.setData(0, Qt.UserRole, "category")

            # 添加子项
            for title, filename in info["items"].items():
                child_item = QTreeWidgetItem(category_item)
                child_item.setText(0, f"📄 {title}")
                child_item.setData(0, Qt.UserRole, filename)

        # 展开所有项
        self.tree_widget.expandAll()

    def on_tree_item_clicked(self, item, _column):
        """处理树项点击事件"""
        data = item.data(0, Qt.UserRole)

        if data == "welcome":
            self.load_welcome_page()
        elif data == "category":
            # 分类项，不做处理
            pass
        elif data and data.endswith(".md"):
            # 帮助文档
            self.load_help_document(data)

    def load_welcome_page(self):
        """加载欢迎页面"""
        try:
            # 优先使用内嵌内容
            if self.embedded_help and "welcome" in self.embedded_help:
                markdown_content = self.embedded_help["welcome"]
                html_content = self.markdown_to_html(markdown_content)
                self.content_browser.setHtml(html_content)
                self.current_doc = "welcome"
                print("使用内嵌欢迎页面内容")
                return
        except Exception as e:
            print(f"加载内嵌欢迎页面失败: {e}")

        # 回退到原有的HTML欢迎页面
        # 检测主题并选择相应的样式
        is_dark_theme = self.is_dark_theme()

        if is_dark_theme:
            welcome_css = """
                body {
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    margin: 20px;
                    background-color: #2b2b2b;
                    color: #e0e0e0;
                }
                h1 { color: #64b5f6; border-bottom: 2px solid #42a5f5; padding-bottom: 10px; }
                h2 { color: #90caf9; margin-top: 30px; }
                .feature { background: #3c3c3c; color: #e0e0e0; padding: 15px; margin: 10px 0; border-radius: 5px; border: 1px solid #555; }
                .tip { background: #2e5d31; color: #a5d6a7; padding: 10px; margin: 10px 0; border-left: 4px solid #66bb6a; }
                ul { line-height: 1.6; }
                a { color: #64b5f6; text-decoration: none; }
                a:hover { text-decoration: underline; color: #90caf9; }
                h3 { color: #81c784; }
                strong { color: #fff; }
            """
        else:
            welcome_css = """
                body {
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    margin: 20px;
                    background-color: #ffffff;
                    color: #333333;
                }
                h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
                h2 { color: #34495e; margin-top: 30px; }
                .feature { background: #f8f9fa; color: #333; padding: 15px; margin: 10px 0; border-radius: 5px; }
                .tip { background: #e8f5e9; color: #2e7d32; padding: 10px; margin: 10px 0; border-left: 4px solid #4caf50; }
                ul { line-height: 1.6; }
                a { color: #3498db; text-decoration: none; }
                a:hover { text-decoration: underline; }
            """

        welcome_html = f"""
        <html>
        <head>
            <style>
                {welcome_css}
            </style>
        </head>
        <body>
            <h1>🎉 欢迎使用SmartVault智能文件管理系统！</h1>

            <div class="tip">
                <strong>💡 新用户建议：</strong>如果您是第一次使用SmartVault，建议从
                <a href="新手指南">新手指南</a> 开始，5分钟就能掌握基本使用方法！
            </div>

            <h2>🎯 SmartVault能为您做什么？</h2>

            <div class="feature">
                <h3>✨ 智能文件整理</h3>
                <ul>
                    <li><strong>一键添加</strong>：将任何文件轻松添加到智能文件库</li>
                    <li><strong>自动分类</strong>：根据文件类型和内容智能分类</li>
                    <li><strong>重复检测</strong>：自动发现并处理重复文件</li>
                </ul>
            </div>

            <div class="feature">
                <h3>🏷️ 灵活标签管理</h3>
                <ul>
                    <li><strong>自由标签</strong>：用任意词语为文件打标签</li>
                    <li><strong>智能建议</strong>：系统自动推荐合适的标签</li>
                    <li><strong>快速筛选</strong>：通过标签快速找到相关文件</li>
                </ul>
            </div>

            <div class="feature">
                <h3>🔍 强大搜索能力</h3>
                <ul>
                    <li><strong>全文搜索</strong>：搜索文件名、标签、备注</li>
                    <li><strong>高级筛选</strong>：按时间、大小、类型等条件筛选</li>
                    <li><strong>即时预览</strong>：搜索结果实时显示</li>
                </ul>
            </div>

            <div class="feature">
                <h3>🤖 自动化助手</h3>
                <ul>
                    <li><strong>文件夹监控</strong>：自动整理指定文件夹的新文件</li>
                    <li><strong>剪贴板监控</strong>：智能处理复制粘贴的文件</li>
                    <li><strong>设备检测</strong>：自动识别U盘等移动设备</li>
                </ul>
            </div>

            <h2>📚 快速导航</h2>
            <ul>
                <li><a href="新手指南">🚀 新手指南</a> - 5分钟快速上手</li>
                <li><a href="基本概念">💡 基本概念</a> - 了解核心概念</li>
                <li><a href="文件管理">📁 文件管理</a> - 学习文件操作</li>
                <li><a href="标签系统">🏷️ 标签系统</a> - 掌握标签技巧</li>
                <li><a href="搜索功能">🔍 搜索功能</a> - 快速查找文件</li>
                <li><a href="常见问题">❓ 常见问题</a> - 解决使用疑问</li>
            </ul>

            <h2>💡 使用小贴士</h2>
            <div class="tip">
                <ul>
                    <li><strong>从小做起</strong>：先添加几个常用文件夹试试</li>
                    <li><strong>善用标签</strong>：给文件打上有意义的标签</li>
                    <li><strong>定期整理</strong>：利用搜索功能定期清理重复文件</li>
                    <li><strong>数据安全</strong>：定期创建备份，重要操作前先备份</li>
                </ul>
            </div>

            <hr>
            <p style="text-align: center; color: #7f8c8d;">
                <strong>SmartVault 3.0</strong> | 帮助文档版本 1.0.0 | 最后更新：2024年12月
            </p>
        </body>
        </html>
        """

        self.content_browser.setHtml(welcome_html)
        self.current_doc = "welcome"

    def load_help_document(self, filename):
        """加载帮助文档

        Args:
            filename: 文档文件名
        """
        try:
            # 优先使用内嵌内容
            if self.embedded_help and filename in self.embedded_help:
                markdown_content = self.embedded_help[filename]
                print(f"使用内嵌帮助内容: {filename}")
            else:
                # 回退到文件系统
                file_path = os.path.join(self.docs_path, filename)
                if not os.path.exists(file_path):
                    self.show_error(f"帮助文档不存在: {filename}")
                    return

                with open(file_path, 'r', encoding='utf-8') as f:
                    markdown_content = f.read()
                print(f"使用文件系统帮助内容: {filename}")

            # 转换Markdown为HTML
            html_content = self.markdown_to_html(markdown_content)
            self.content_browser.setHtml(html_content)
            self.current_doc = filename

        except Exception as e:
            self.show_error(f"加载帮助文档失败: {e}")

    def markdown_to_html(self, markdown_text):
        """简单的Markdown到HTML转换

        Args:
            markdown_text: Markdown文本

        Returns:
            str: HTML文本
        """
        # 这是一个简化的Markdown转换器，只处理基本格式
        html = markdown_text

        # 检测当前主题
        is_dark_theme = self.is_dark_theme()

        # 根据主题选择样式
        if is_dark_theme:
            html_header = self.get_dark_theme_css()
        else:
            html_header = self.get_light_theme_css()

        html_footer = """
        </body>
        </html>
        """

        # 基本的Markdown转换
        import re

        # 标题
        html = re.sub(r'^# (.+)$', r'<h1>\1</h1>', html, flags=re.MULTILINE)
        html = re.sub(r'^## (.+)$', r'<h2>\1</h2>', html, flags=re.MULTILINE)
        html = re.sub(r'^### (.+)$', r'<h3>\1</h3>', html, flags=re.MULTILINE)
        html = re.sub(r'^#### (.+)$', r'<h4>\1</h4>', html, flags=re.MULTILINE)

        # 引用块
        html = re.sub(r'^> (.+)$', r'<blockquote>\1</blockquote>', html, flags=re.MULTILINE)

        # 代码块
        html = re.sub(r'```([^`]+)```', r'<pre><code>\1</code></pre>', html, flags=re.DOTALL)

        # 行内代码
        html = re.sub(r'`([^`]+)`', r'<code>\1</code>', html)

        # 粗体
        html = re.sub(r'\*\*([^*]+)\*\*', r'<strong>\1</strong>', html)

        # 斜体
        html = re.sub(r'\*([^*]+)\*', r'<em>\1</em>', html)

        # 链接（内部链接）
        html = re.sub(r'\[([^\]]+)\]\(\.\/([^)]+)\.md\)', r'<a href="\2">\1</a>', html)

        # 换行
        html = html.replace('\n', '<br>\n')

        return html_header + html + html_footer

    def is_dark_theme(self):
        """检测当前是否为深色主题

        Returns:
            bool: 是否为深色主题
        """
        try:
            # 获取当前窗口的背景色
            palette = self.palette()
            bg_color = palette.color(palette.Window)

            # 计算亮度 (使用相对亮度公式)
            luminance = (0.299 * bg_color.red() + 0.587 * bg_color.green() + 0.114 * bg_color.blue()) / 255

            # 如果亮度小于0.5，认为是深色主题
            return luminance < 0.5
        except Exception:
            return False

    def get_light_theme_css(self):
        """获取浅色主题CSS

        Returns:
            str: CSS样式
        """
        return """
        <html>
        <head>
            <style>
                body {
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    background-color: #ffffff;
                    color: #333333;
                }
                h1 { color: #2c3e50; border-bottom: 2px solid #3498db; padding-bottom: 10px; }
                h2 { color: #34495e; margin-top: 25px; }
                h3 { color: #2c3e50; margin-top: 20px; }
                h4 { color: #34495e; margin-top: 15px; }
                code {
                    background: #f4f4f4;
                    color: #333;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: Consolas, monospace;
                }
                pre {
                    background: #f8f8f8;
                    color: #333;
                    padding: 15px;
                    border-radius: 5px;
                    overflow-x: auto;
                }
                blockquote {
                    background: #f9f9f9;
                    color: #555;
                    border-left: 4px solid #ddd;
                    margin: 0;
                    padding: 10px 20px;
                }
                table { border-collapse: collapse; width: 100%; margin: 15px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; color: #333; }
                th { background-color: #f2f2f2; }
                ul, ol { margin: 10px 0; padding-left: 30px; }
                li { margin: 5px 0; }
                a { color: #3498db; text-decoration: none; }
                a:hover { text-decoration: underline; }
                .tip { background: #e8f5e9; color: #2e7d32; padding: 10px; margin: 10px 0; border-left: 4px solid #4caf50; }
                .warning { background: #fff3cd; color: #856404; padding: 10px; margin: 10px 0; border-left: 4px solid #ffc107; }
                .error { background: #f8d7da; color: #721c24; padding: 10px; margin: 10px 0; border-left: 4px solid #dc3545; }
            </style>
        </head>
        <body>
        """

    def get_dark_theme_css(self):
        """获取深色主题CSS

        Returns:
            str: CSS样式
        """
        return """
        <html>
        <head>
            <style>
                body {
                    font-family: "Microsoft YaHei", Arial, sans-serif;
                    margin: 20px;
                    line-height: 1.6;
                    background-color: #2b2b2b;
                    color: #e0e0e0;
                }
                h1 { color: #64b5f6; border-bottom: 2px solid #42a5f5; padding-bottom: 10px; }
                h2 { color: #90caf9; margin-top: 25px; }
                h3 { color: #64b5f6; margin-top: 20px; }
                h4 { color: #90caf9; margin-top: 15px; }
                code {
                    background: #3c3c3c;
                    color: #f8f8f2;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: Consolas, monospace;
                }
                pre {
                    background: #3c3c3c;
                    color: #f8f8f2;
                    padding: 15px;
                    border-radius: 5px;
                    overflow-x: auto;
                    border: 1px solid #555;
                }
                blockquote {
                    background: #3c3c3c;
                    color: #b0b0b0;
                    border-left: 4px solid #666;
                    margin: 0;
                    padding: 10px 20px;
                }
                table { border-collapse: collapse; width: 100%; margin: 15px 0; }
                th, td { border: 1px solid #555; padding: 8px; text-align: left; color: #e0e0e0; }
                th { background-color: #404040; }
                ul, ol { margin: 10px 0; padding-left: 30px; }
                li { margin: 5px 0; }
                a { color: #64b5f6; text-decoration: none; }
                a:hover { text-decoration: underline; color: #90caf9; }
                .tip { background: #2e5d31; color: #a5d6a7; padding: 10px; margin: 10px 0; border-left: 4px solid #66bb6a; }
                .warning { background: #5d4e00; color: #fff3a0; padding: 10px; margin: 10px 0; border-left: 4px solid #ffca28; }
                .error { background: #5d1a1a; color: #ffcdd2; padding: 10px; margin: 10px 0; border-left: 4px solid #f44336; }
            </style>
        </head>
        <body>
        """

    def on_link_clicked(self, url):
        """处理链接点击事件

        Args:
            url: 点击的URL
        """
        url_str = url.toString()

        # 处理内部链接
        if not url_str.startswith('http'):
            # 查找对应的文档文件
            for _category, info in self.help_structure.items():
                for title, filename in info["items"].items():
                    if title == url_str or filename.replace('.md', '') == url_str or url_str in title:
                        self.load_help_document(filename)
                        # 在树中选中对应项
                        self.select_tree_item(title)
                        return

            # 如果没找到，显示错误
            self.show_error(f"未找到帮助文档: {url_str}")
        else:
            # 外部链接，用系统默认浏览器打开
            QDesktopServices.openUrl(url)

    def select_tree_item(self, title):
        """在树中选中指定项

        Args:
            title: 要选中的项标题
        """
        for i in range(self.tree_widget.topLevelItemCount()):
            top_item = self.tree_widget.topLevelItem(i)
            for j in range(top_item.childCount()):
                child_item = top_item.child(j)
                if title in child_item.text(0):
                    self.tree_widget.setCurrentItem(child_item)
                    return

    def on_search_text_changed(self, text):
        """搜索文本改变时的处理"""
        if len(text) >= 2:  # 至少2个字符才开始搜索
            self.highlight_search_results(text)

    def search_help(self):
        """搜索帮助内容"""
        search_text = self.search_edit.text().strip()
        if not search_text:
            return

        # 在当前文档中搜索
        if self.current_doc and self.current_doc != "welcome":
            self.search_in_current_document(search_text)
        else:
            # 全局搜索
            self.search_all_documents(search_text)

    def highlight_search_results(self, search_text):
        """高亮搜索结果

        Args:
            search_text: 搜索文本
        """
        if not search_text or len(search_text) < 2:
            return

        # 在内容浏览器中高亮显示
        cursor = self.content_browser.textCursor()
        cursor.movePosition(cursor.Start)

        # 清除之前的高亮
        self.content_browser.setTextCursor(cursor)

        # 查找并高亮
        found = self.content_browser.find(search_text)
        if found:
            # 滚动到第一个匹配项
            self.content_browser.ensureCursorVisible()

    def search_in_current_document(self, search_text):
        """在当前文档中搜索

        Args:
            search_text: 搜索文本
        """
        self.highlight_search_results(search_text)

    def search_all_documents(self, search_text):
        """在所有文档中搜索

        Args:
            search_text: 搜索文本
        """
        results = []
        search_text_lower = search_text.lower()

        # 搜索所有帮助文档
        for category, info in self.help_structure.items():
            for title, filename in info["items"].items():
                try:
                    file_path = os.path.join(self.docs_path, filename)
                    if os.path.exists(file_path):
                        with open(file_path, 'r', encoding='utf-8') as f:
                            content = f.read().lower()
                            if search_text_lower in content:
                                results.append((title, filename, category))
                except Exception:
                    continue

        # 显示搜索结果
        if results:
            self.show_search_results(search_text, results)
        else:
            QMessageBox.information(self, "搜索结果", f"未找到包含 '{search_text}' 的帮助内容。")

    def show_search_results(self, search_text, results):
        """显示搜索结果

        Args:
            search_text: 搜索文本
            results: 搜索结果列表
        """
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: "Microsoft YaHei", Arial, sans-serif; margin: 20px; }}
                h1 {{ color: #2c3e50; }}
                .result {{ background: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; }}
                .result h3 {{ margin-top: 0; }}
                .category {{ color: #7f8c8d; font-size: 0.9em; }}
                a {{ color: #3498db; text-decoration: none; }}
                a:hover {{ text-decoration: underline; }}
            </style>
        </head>
        <body>
            <h1>🔍 搜索结果</h1>
            <p>搜索关键词: <strong>{search_text}</strong></p>
            <p>找到 {len(results)} 个相关帮助文档：</p>
        """

        for title, filename, category in results:
            html += f"""
            <div class="result">
                <h3><a href="{filename}">{title}</a></h3>
                <div class="category">分类: {category}</div>
            </div>
            """

        html += """
        </body>
        </html>
        """

        self.content_browser.setHtml(html)
        self.current_doc = "search_results"

    def open_docs_folder(self):
        """打开文档文件夹"""
        try:
            if os.path.exists(self.docs_path):
                if os.name == 'nt':  # Windows
                    os.startfile(self.docs_path)
                else:  # macOS and Linux
                    QDesktopServices.openUrl(QUrl.fromLocalFile(self.docs_path))
            else:
                QMessageBox.warning(self, "警告", "文档文件夹不存在。")
        except Exception as e:
            self.show_error(f"打开文档文件夹失败: {e}")

    def show_error(self, message):
        """显示错误消息

        Args:
            message: 错误消息
        """
        QMessageBox.critical(self, "错误", message)

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == Qt.Key_F3:
            # F3继续搜索
            search_text = self.search_edit.text().strip()
            if search_text:
                self.content_browser.find(search_text)
        elif event.key() == Qt.Key_Escape:
            # ESC关闭对话框
            self.close()
        else:
            super().keyPressEvent(event)
