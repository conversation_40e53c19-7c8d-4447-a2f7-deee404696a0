"""
智能规则引擎

实现基于规则的"伪AI"智能效果，包括：
- 项目文件检测
- 系列文件识别
- 行为模式学习
- 智能命名建议
"""

import os
import re
import json
from typing import Dict, List, Optional, Any
from datetime import datetime
from collections import defaultdict, Counter


class SmartRuleEngine:
    """智能规则引擎 - 第一阶段实现"""

    def __init__(self):
        self.tag_service = None
        self.db = None  # 数据库连接
        self.learning_patterns = {}
        self.project_indicators = self._init_project_indicators()
        self.series_patterns = self._init_series_patterns()

    def initialize(self, config: Dict, tag_service=None, db=None):
        """初始化规则引擎

        Args:
            config: 配置字典
            tag_service: 标签服务实例
            db: 数据库连接
        """
        self.tag_service = tag_service
        self.db = db

        # 加载已有的学习模式
        if self.db:
            self._load_learning_patterns()

        # TODO: 从配置加载自定义规则

    def _init_project_indicators(self) -> Dict:
        """初始化项目特征指示器"""
        return {
            'code_extensions': {
                '.py', '.js', '.ts', '.jsx', '.tsx', '.html', '.css', '.scss', '.sass', '.less',
                '.java', '.kt', '.scala', '.cpp', '.c', '.h', '.hpp', '.cs', '.vb',
                '.php', '.rb', '.go', '.rs', '.swift', '.dart', '.lua', '.r', '.m',
                '.sh', '.bat', '.ps1', '.sql', '.pl', '.asm', '.f90', '.pas'
            },
            'config_files': {
                'package.json', 'package-lock.json', 'yarn.lock', 'pnpm-lock.yaml',
                'requirements.txt', 'Pipfile', 'pyproject.toml', 'setup.py', 'setup.cfg',
                'pom.xml', 'build.gradle', 'build.gradle.kts', 'settings.gradle',
                'Cargo.toml', 'Cargo.lock', 'composer.json', 'composer.lock',
                'Gemfile', 'Gemfile.lock', 'go.mod', 'go.sum',
                'CMakeLists.txt', 'Makefile', 'makefile', 'configure.ac',
                'webpack.config.js', 'vite.config.js', 'rollup.config.js',
                'tsconfig.json', 'jsconfig.json', '.eslintrc.json', '.prettierrc'
            },
            'project_files': {
                'README.md', 'README.txt', 'README.rst', 'README',
                'LICENSE', 'LICENSE.txt', 'LICENSE.md', 'COPYING',
                'CHANGELOG.md', 'CHANGELOG.txt', 'HISTORY.md',
                'CONTRIBUTING.md', 'CODE_OF_CONDUCT.md',
                'Dockerfile', 'docker-compose.yml', 'docker-compose.yaml',
                '.gitignore', '.gitattributes', '.dockerignore',
                '.env', '.env.example', '.env.local'
            },
            'doc_extensions': {'.md', '.txt', '.rst', '.doc', '.docx', '.pdf', '.tex', '.adoc'},
            'build_artifacts': {
                'node_modules', '__pycache__', '.git', '.svn', '.hg',
                'target', 'build', 'dist', 'out', 'bin', 'obj',
                '.idea', '.vscode', '.vs', '*.egg-info'
            },
            'project_types': {
                'web': {
                    'indicators': ['package.json', 'index.html', 'webpack.config.js'],
                    'extensions': {'.js', '.ts', '.html', '.css', '.jsx', '.tsx'},
                    'keywords': ['react', 'vue', 'angular', 'webpack', 'vite']
                },
                'python': {
                    'indicators': ['requirements.txt', 'setup.py', 'pyproject.toml'],
                    'extensions': {'.py'},
                    'keywords': ['django', 'flask', 'fastapi', 'pytest']
                },
                'java': {
                    'indicators': ['pom.xml', 'build.gradle'],
                    'extensions': {'.java', '.kt', '.scala'},
                    'keywords': ['spring', 'maven', 'gradle']
                },
                'mobile': {
                    'indicators': ['pubspec.yaml', 'ios', 'android'],
                    'extensions': {'.dart', '.swift', '.kt', '.java'},
                    'keywords': ['flutter', 'react-native', 'ionic']
                }
            },
            'min_files_for_project': 3,
            'min_diversity_score': 0.25,
            'confidence_thresholds': {
                'high': 0.85,
                'medium': 0.65,
                'low': 0.45
            }
        }

    def _init_series_patterns(self) -> List[Dict]:
        """初始化系列模式（增强版）"""
        return [
            {
                'name': 'version_pattern',
                'regex': r'(.+?)[-_\s]*v?(\d+)(?:\.(\d+))?(?:\.(\d+))?(?:[-_]?(alpha|beta|rc|release|final))?',
                'description': '版本模式 (v1.0, v2.1.3, v1.0-beta等)',
                'priority': 0.9,
                'confidence_boost': 0.1
            },
            {
                'name': 'semantic_version',
                'regex': r'(.+?)[-_\s]*(\d+)\.(\d+)\.(\d+)(?:[-_]?(alpha|beta|rc|pre|post)\.?(\d+)?)?',
                'description': '语义化版本 (1.0.0, 2.1.3-beta.1等)',
                'priority': 0.95,
                'confidence_boost': 0.15
            },
            {
                'name': 'date_pattern',
                'regex': r'(.+?)[-_\s]*(\d{4})[-_]?(\d{1,2})[-_]?(\d{1,2})?',
                'description': '日期模式 (2024-01, 20240101等)',
                'priority': 0.8,
                'confidence_boost': 0.1
            },
            {
                'name': 'year_month_pattern',
                'regex': r'(.+?)[-_\s]*(\d{4})[-_](\d{1,2})',
                'description': '年月模式 (2024-01, 2024_12等)',
                'priority': 0.85,
                'confidence_boost': 0.12
            },
            {
                'name': 'sequence_pattern',
                'regex': r'(.+?)[-_\s]*(part|chapter|section|episode|第|篇|章|节|集)[-_\s]*(\d+)',
                'description': '序列模式 (part1, chapter2, 第3章, 第1集等)',
                'priority': 0.9,
                'confidence_boost': 0.15
            },
            {
                'name': 'numbered_pattern',
                'regex': r'(.+?)[-_\s]*(\d+)(?!.*\d)',
                'description': '编号模式 (file1, document2等)',
                'priority': 0.6,
                'confidence_boost': 0.05
            },
            {
                'name': 'alphabetic_series',
                'regex': r'(.+?)[-_\s]*([a-zA-Z])(?!.*[a-zA-Z])',
                'description': '字母序列 (fileA, documentB等)',
                'priority': 0.7,
                'confidence_boost': 0.08
            },
            {
                'name': 'roman_numeral',
                'regex': r'(.+?)[-_\s]*(I{1,3}|IV|V|VI{0,3}|IX|X|XI{0,3}|XII{0,3})(?!.*[IVX])',
                'description': '罗马数字 (fileI, documentII等)',
                'priority': 0.75,
                'confidence_boost': 0.1
            },
            {
                'name': 'revision_pattern',
                'regex': r'(.+?)[-_\s]*(rev|revision|r)[-_\s]*(\d+)',
                'description': '修订版本 (rev1, revision2, r3等)',
                'priority': 0.85,
                'confidence_boost': 0.12
            },
            {
                'name': 'draft_pattern',
                'regex': r'(.+?)[-_\s]*(draft|草稿|初稿)[-_\s]*(\d+)',
                'description': '草稿版本 (draft1, 草稿2等)',
                'priority': 0.8,
                'confidence_boost': 0.1
            },
            {
                'name': 'backup_pattern',
                'regex': r'(.+?)[-_\s]*(backup|bak|备份)[-_\s]*(\d+)',
                'description': '备份版本 (backup1, 备份2等)',
                'priority': 0.75,
                'confidence_boost': 0.08
            },
            {
                'name': 'copy_pattern',
                'regex': r'(.+?)[-_\s]*(copy|副本|拷贝)[-_\s]*(\d+)',
                'description': '副本版本 (copy1, 副本2等)',
                'priority': 0.7,
                'confidence_boost': 0.06
            }
        ]

    def suggest_tags(self, file_info: Dict) -> List[str]:
        """智能标签建议

        Args:
            file_info: 文件信息字典

        Returns:
            List[str]: 建议的标签列表
        """
        suggestions = []

        # 基于文件类型的智能建议
        type_tags = self._suggest_by_file_type(file_info)
        suggestions.extend(type_tags)

        # 基于文件名模式的建议
        pattern_tags = self._suggest_by_name_patterns(file_info)
        suggestions.extend(pattern_tags)

        # 基于路径的建议
        path_tags = self._suggest_by_path(file_info)
        suggestions.extend(path_tags)

        # 基于学习的用户偏好
        preference_tags = self._suggest_by_user_preferences(file_info)
        suggestions.extend(preference_tags)

        # 去重并保持顺序
        return list(dict.fromkeys(suggestions))[:8]  # 最多返回8个建议

    def _suggest_by_file_type(self, file_info: Dict) -> List[str]:
        """基于文件类型的智能建议"""
        suggestions = []
        extension = file_info.get('extension', '').lower()
        filename = file_info.get('name', '').lower()

        # 代码文件的智能分类
        if extension in self.project_indicators['code_extensions']:
            suggestions.append('代码')

            # 更具体的语言标签
            lang_mapping = {
                '.py': 'Python', '.js': 'JavaScript', '.html': 'HTML',
                '.css': 'CSS', '.java': 'Java', '.cpp': 'C++',
                '.c': 'C语言', '.php': 'PHP', '.rb': 'Ruby'
            }
            if extension in lang_mapping:
                suggestions.append(lang_mapping[extension])

            # 检测特殊用途
            if 'test' in filename:
                suggestions.append('测试代码')
            elif 'config' in filename or 'setting' in filename:
                suggestions.append('配置文件')
            elif 'util' in filename or 'helper' in filename:
                suggestions.append('工具代码')

        # 文档文件的智能分类
        elif extension in self.project_indicators['doc_extensions']:
            suggestions.append('文档')

            if 'readme' in filename:
                suggestions.append('说明文档')
            elif 'manual' in filename or '手册' in filename:
                suggestions.append('用户手册')
            elif 'api' in filename:
                suggestions.append('API文档')
            elif 'spec' in filename or '规格' in filename:
                suggestions.append('规格文档')

        return suggestions

    def _suggest_by_name_patterns(self, file_info: Dict) -> List[str]:
        """基于文件名模式的建议"""
        suggestions = []
        filename = file_info.get('name', '').lower()

        # 重要性指示器
        if any(word in filename for word in ['important', '重要', 'critical', '关键']):
            suggestions.append('重要')

        if any(word in filename for word in ['urgent', '紧急', 'asap']):
            suggestions.append('紧急')

        if any(word in filename for word in ['final', '最终', 'release']):
            suggestions.append('最终版')

        if any(word in filename for word in ['draft', '草稿', 'temp', '临时']):
            suggestions.append('草稿')

        if any(word in filename for word in ['backup', '备份', 'bak']):
            suggestions.append('备份')

        # 工作类型指示器
        if any(word in filename for word in ['meeting', '会议', 'conference']):
            suggestions.append('会议')

        if any(word in filename for word in ['report', '报告', 'summary']):
            suggestions.append('报告')

        if any(word in filename for word in ['proposal', '提案', 'plan', '计划']):
            suggestions.append('计划')

        if any(word in filename for word in ['presentation', '演示', 'ppt']):
            suggestions.append('演示文稿')

        return suggestions

    def _suggest_by_path(self, file_info: Dict) -> List[str]:
        """基于文件路径的建议"""
        suggestions = []
        path = file_info.get('path', '').lower()

        # 基于路径中的关键词
        path_keywords = {
            'work': '工作',
            'personal': '个人',
            'project': '项目',
            'download': '下载',
            'desktop': '桌面',
            'document': '文档',
            'picture': '图片',
            'video': '视频',
            'music': '音乐'
        }

        for keyword, tag in path_keywords.items():
            if keyword in path or tag in path:
                suggestions.append(tag)

        return suggestions

    def _suggest_by_user_preferences(self, file_info: Dict) -> List[str]:
        """基于学习的用户偏好建议标签"""
        try:
            pattern_key = self._generate_pattern_key({'file_info': file_info})

            if pattern_key in self.learning_patterns:
                pattern = self.learning_patterns[pattern_key]

                # 按使用频率排序标签
                sorted_tags = sorted(
                    pattern['tag_preferences'].items(),
                    key=lambda x: x[1],
                    reverse=True
                )

                # 只返回使用频率较高的标签（至少使用过2次）
                return [tag for tag, freq in sorted_tags[:3] if freq >= 2]

            return []
        except Exception as e:
            print(f"用户偏好建议失败: {e}")
            return []

    def detect_project_files(self, folder_path: str) -> Dict:
        """检测项目文件并建议标签（增强版）

        Args:
            folder_path: 文件夹路径

        Returns:
            Dict: 项目检测结果
        """
        try:
            if not os.path.exists(folder_path):
                return {}

            files = self._get_folder_files(folder_path)
            if len(files) < self.project_indicators['min_files_for_project']:
                return {}

            # 分析文件类型分布
            type_stats = self._analyze_file_types(files)

            # 检测项目类型
            project_type_info = self._detect_project_type(files)

            # 项目特征检测（增强版）
            project_indicators = {
                'has_code_files': bool(type_stats['code_files']),
                'has_config_files': bool(type_stats['config_files']),
                'has_project_files': bool(type_stats['project_files']),
                'has_docs': bool(type_stats['doc_files']),
                'has_build_artifacts': bool(type_stats.get('build_artifacts', 0)),
                'file_count': len(files),
                'diversity_score': len([k for k, v in type_stats.items() if v > 0]) / len(type_stats),
                'project_type': project_type_info['type'],
                'type_confidence': project_type_info['confidence'],
                'complexity_score': self._calculate_project_complexity(files, type_stats)
            }

            # 判断是否为项目文件夹
            if self._is_project_folder(project_indicators):
                project_name = self._extract_project_name(folder_path, files)
                confidence = self._calculate_project_confidence(project_indicators)

                # 生成智能标签
                suggested_tags = self._generate_project_tags(
                    project_name,
                    project_type_info,
                    project_indicators
                )

                return {
                    'project_name': project_name,
                    'project_type': project_type_info['type'],
                    'suggested_tags': suggested_tags,
                    'confidence': confidence,
                    'affected_files': files,
                    'indicators': project_indicators,
                    'complexity': self._get_complexity_level(project_indicators['complexity_score']),
                    'recommendations': self._generate_project_recommendations(project_indicators)
                }

        except Exception as e:
            print(f"项目文件检测失败: {e}")

        return {}

    def _detect_project_type(self, files: List[Dict]) -> Dict:
        """检测项目类型

        Args:
            files: 文件列表

        Returns:
            Dict: 项目类型信息
        """
        type_scores = {}

        for project_type, type_info in self.project_indicators['project_types'].items():
            score = 0.0

            # 检查指示器文件
            for indicator in type_info['indicators']:
                if any(f['full_name'].lower() == indicator.lower() for f in files):
                    score += 0.4

            # 检查文件扩展名
            type_extensions = type_info['extensions']
            matching_files = sum(1 for f in files if f['extension'].lower() in type_extensions)
            if matching_files > 0:
                score += min(0.3, matching_files / len(files))

            # 检查关键词（在文件名或内容中）
            for keyword in type_info['keywords']:
                if any(keyword.lower() in f['full_name'].lower() for f in files):
                    score += 0.1

            type_scores[project_type] = score

        # 找到最高分的项目类型
        if type_scores:
            best_type = max(type_scores, key=type_scores.get)
            best_score = type_scores[best_type]

            if best_score > 0.3:  # 最低置信度阈值
                return {
                    'type': best_type,
                    'confidence': min(1.0, best_score),
                    'all_scores': type_scores
                }

        return {
            'type': 'general',
            'confidence': 0.5,
            'all_scores': type_scores
        }

    def _calculate_project_complexity(self, files: List[Dict], type_stats: Dict) -> float:
        """计算项目复杂度

        Args:
            files: 文件列表
            type_stats: 文件类型统计

        Returns:
            float: 复杂度评分 (0.0-1.0)
        """
        complexity = 0.0

        # 基于文件数量
        file_count = len(files)
        if file_count > 100:
            complexity += 0.3
        elif file_count > 50:
            complexity += 0.2
        elif file_count > 20:
            complexity += 0.1

        # 基于文件类型多样性
        type_diversity = len([k for k, v in type_stats.items() if isinstance(v, int) and v > 0])
        complexity += min(0.3, type_diversity * 0.05)

        # 基于配置文件数量
        config_count = type_stats.get('config_files', 0)
        complexity += min(0.2, config_count * 0.05)

        # 基于代码文件比例
        code_ratio = type_stats.get('code_files', 0) / max(file_count, 1)
        if code_ratio > 0.5:
            complexity += 0.2

        return min(1.0, complexity)

    def _generate_project_tags(self, project_name: str, type_info: Dict, indicators: Dict) -> List[str]:
        """生成项目标签

        Args:
            project_name: 项目名称
            type_info: 项目类型信息
            indicators: 项目指标

        Returns:
            List[str]: 标签列表
        """
        tags = []

        # 基础项目标签
        tags.append(f"📁{project_name}")
        tags.append("项目文件")

        # 项目类型标签
        project_type = type_info['type']
        if project_type != 'general':
            type_names = {
                'web': 'Web项目',
                'python': 'Python项目',
                'java': 'Java项目',
                'mobile': '移动应用'
            }
            if project_type in type_names:
                tags.append(type_names[project_type])

        # 复杂度标签
        complexity_level = self._get_complexity_level(indicators['complexity_score'])
        if complexity_level != 'simple':
            complexity_names = {
                'complex': '复杂项目',
                'medium': '中型项目'
            }
            if complexity_level in complexity_names:
                tags.append(complexity_names[complexity_level])

        # 特征标签
        if indicators['has_docs']:
            tags.append("有文档")

        if indicators['has_config_files']:
            tags.append("已配置")

        if indicators['file_count'] > 50:
            tags.append("大型项目")
        elif indicators['file_count'] > 20:
            tags.append("中型项目")

        return tags

    def _get_complexity_level(self, complexity_score: float) -> str:
        """获取复杂度级别

        Args:
            complexity_score: 复杂度评分

        Returns:
            str: 复杂度级别
        """
        if complexity_score > 0.7:
            return 'complex'
        elif complexity_score > 0.4:
            return 'medium'
        else:
            return 'simple'

    def _generate_project_recommendations(self, indicators: Dict) -> List[str]:
        """生成项目建议

        Args:
            indicators: 项目指标

        Returns:
            List[str]: 建议列表
        """
        recommendations = []

        if not indicators['has_docs']:
            recommendations.append("建议添加README文档")

        if not indicators['has_config_files'] and indicators['has_code_files']:
            recommendations.append("建议添加配置文件")

        if indicators['file_count'] > 100 and indicators['complexity_score'] > 0.8:
            recommendations.append("项目较复杂，建议进行模块化管理")

        return recommendations

    def _get_folder_files(self, folder_path: str) -> List[Dict]:
        """获取文件夹中的文件信息"""
        files = []
        try:
            for item in os.listdir(folder_path):
                item_path = os.path.join(folder_path, item)
                if os.path.isfile(item_path):
                    name, ext = os.path.splitext(item)
                    files.append({
                        'name': name,
                        'extension': ext,
                        'full_name': item,
                        'path': item_path
                    })
        except Exception as e:
            print(f"读取文件夹失败: {e}")

        return files

    def _analyze_file_types(self, files: List[Dict]) -> Dict:
        """分析文件类型分布（增强版）"""
        stats = {
            'code_files': 0,
            'config_files': 0,
            'project_files': 0,
            'doc_files': 0,
            'build_artifacts': 0,
            'other_files': 0,
            'total_files': len(files)
        }

        # 详细统计
        extension_counts = defaultdict(int)
        language_counts = defaultdict(int)

        for file in files:
            ext = file['extension'].lower()
            name = file['full_name'].lower()

            # 统计扩展名
            extension_counts[ext] += 1

            # 分类统计
            if ext in self.project_indicators['code_extensions']:
                stats['code_files'] += 1
                # 识别编程语言
                language = self._identify_language(ext)
                if language:
                    language_counts[language] += 1

            elif name in self.project_indicators['config_files']:
                stats['config_files'] += 1

            elif name in self.project_indicators['project_files']:
                stats['project_files'] += 1

            elif ext in self.project_indicators['doc_extensions']:
                stats['doc_files'] += 1

            elif self._is_build_artifact(name):
                stats['build_artifacts'] += 1

            else:
                stats['other_files'] += 1

        # 添加详细信息
        stats['extension_distribution'] = dict(extension_counts)
        stats['language_distribution'] = dict(language_counts)
        stats['primary_language'] = max(language_counts, key=language_counts.get) if language_counts else None

        return stats

    def _identify_language(self, extension: str) -> str:
        """识别编程语言

        Args:
            extension: 文件扩展名

        Returns:
            str: 编程语言名称
        """
        language_map = {
            '.py': 'Python',
            '.js': 'JavaScript',
            '.ts': 'TypeScript',
            '.jsx': 'React',
            '.tsx': 'React',
            '.java': 'Java',
            '.kt': 'Kotlin',
            '.scala': 'Scala',
            '.cpp': 'C++',
            '.c': 'C',
            '.h': 'C/C++',
            '.hpp': 'C++',
            '.cs': 'C#',
            '.php': 'PHP',
            '.rb': 'Ruby',
            '.go': 'Go',
            '.rs': 'Rust',
            '.swift': 'Swift',
            '.dart': 'Dart',
            '.html': 'HTML',
            '.css': 'CSS',
            '.scss': 'SCSS',
            '.sass': 'Sass',
            '.less': 'Less'
        }
        return language_map.get(extension.lower(), '')

    def _is_build_artifact(self, filename: str) -> bool:
        """判断是否为构建产物

        Args:
            filename: 文件名

        Returns:
            bool: 是否为构建产物
        """
        filename_lower = filename.lower()

        # 检查是否为构建产物目录或文件
        for artifact in self.project_indicators['build_artifacts']:
            if artifact.startswith('*'):
                # 通配符匹配
                pattern = artifact.replace('*', '')
                if pattern in filename_lower:
                    return True
            else:
                if artifact == filename_lower:
                    return True

        return False

    def _is_project_folder(self, indicators: Dict) -> bool:
        """判断是否为项目文件夹"""
        # 至少要有代码文件或配置文件
        has_project_markers = (
            indicators['has_code_files'] or
            indicators['has_config_files'] or
            indicators['has_project_files']
        )

        # 文件多样性要足够
        diversity_ok = indicators['diversity_score'] >= self.project_indicators['min_diversity_score']

        # 文件数量要足够
        count_ok = indicators['file_count'] >= self.project_indicators['min_files_for_project']

        return has_project_markers and diversity_ok and count_ok

    def _extract_project_name(self, folder_path: str, files: List[Dict]) -> str:
        """提取项目名称"""
        # 优先使用文件夹名称
        folder_name = os.path.basename(folder_path)
        if folder_name and folder_name != '.':
            return folder_name

        # 如果有package.json，尝试从中提取名称
        for file in files:
            if file['full_name'] == 'package.json':
                try:
                    with open(file['path'], 'r', encoding='utf-8') as f:
                        package_data = json.load(f)
                        if 'name' in package_data:
                            return package_data['name']
                except:
                    pass

        # 使用最常见的文件名前缀
        name_prefixes = []
        for file in files:
            parts = file['name'].split('_')
            if len(parts) > 1:
                name_prefixes.append(parts[0])

        if name_prefixes:
            most_common = Counter(name_prefixes).most_common(1)[0][0]
            return most_common

        return "未知项目"

    def _calculate_project_confidence(self, indicators: Dict) -> float:
        """计算项目检测的置信度"""
        confidence = 0.0

        if indicators['has_code_files']:
            confidence += 0.4
        if indicators['has_config_files']:
            confidence += 0.3
        if indicators['has_project_files']:
            confidence += 0.2
        if indicators['has_docs']:
            confidence += 0.1

        # 基于多样性调整
        diversity_bonus = min(0.2, indicators['diversity_score'])
        confidence += diversity_bonus

        return min(1.0, confidence)

    def detect_file_series(self, files: List[Dict]) -> List[Dict]:
        """检测文件系列

        Args:
            files: 文件列表

        Returns:
            List[Dict]: 系列检测结果
        """
        if len(files) < 2:
            return []

        series_groups = []

        try:
            # 基于文件名相似性分组
            similarity_groups = self._group_by_similarity(files)

            for group in similarity_groups:
                if len(group) >= 2:  # 至少2个文件才算系列
                    series_info = self._analyze_series(group)
                    if series_info['confidence'] > 0.6:
                        series_groups.append({
                            'series_name': series_info['name'],
                            'files': group,
                            'suggested_tag': f"📚{series_info['name']}系列",
                            'pattern_type': series_info['pattern'],
                            'confidence': series_info['confidence']
                        })

        except Exception as e:
            print(f"文件系列检测失败: {e}")

        return series_groups

    def _group_by_similarity(self, files: List[Dict]) -> List[List[Dict]]:
        """基于文件名相似性分组"""
        groups = []
        processed = set()

        for i, file1 in enumerate(files):
            if i in processed:
                continue

            group = [file1]
            processed.add(i)

            for j, file2 in enumerate(files[i+1:], i+1):
                if j in processed:
                    continue

                if self._are_files_similar(file1, file2):
                    group.append(file2)
                    processed.add(j)

            if len(group) >= 2:
                groups.append(group)

        return groups

    def _are_files_similar(self, file1: Dict, file2: Dict) -> bool:
        """判断两个文件是否相似（可能属于同一系列）- 增强版"""
        name1 = file1['name'].lower()
        name2 = file2['name'].lower()

        # 扩展名必须相同
        if file1['extension'] != file2['extension']:
            return False

        # 按优先级排序的模式匹配
        sorted_patterns = sorted(self.series_patterns, key=lambda x: x['priority'], reverse=True)

        for pattern_info in sorted_patterns:
            match1 = re.search(pattern_info['regex'], name1, re.IGNORECASE)
            match2 = re.search(pattern_info['regex'], name2, re.IGNORECASE)

            if match1 and match2:
                # 提取基础名称（去除版本/序号部分）
                base1 = match1.group(1).strip()
                base2 = match2.group(1).strip()

                # 基础名称相似度检查（根据模式优先级调整阈值）
                similarity_threshold = 0.8 - (pattern_info['priority'] - 0.6) * 0.2
                similarity_threshold = max(0.6, min(0.9, similarity_threshold))

                if self._calculate_string_similarity(base1, base2) > similarity_threshold:
                    return True

        # 简单的字符串相似度检查（降低阈值）
        similarity = self._calculate_string_similarity(name1, name2)
        return similarity > 0.75

    def _calculate_string_similarity(self, str1: str, str2: str) -> float:
        """计算字符串相似度（简单的编辑距离算法）"""
        if not str1 or not str2:
            return 0.0

        if str1 == str2:
            return 1.0

        # 计算最长公共子序列长度
        m, n = len(str1), len(str2)
        dp = [[0] * (n + 1) for _ in range(m + 1)]

        for i in range(1, m + 1):
            for j in range(1, n + 1):
                if str1[i-1] == str2[j-1]:
                    dp[i][j] = dp[i-1][j-1] + 1
                else:
                    dp[i][j] = max(dp[i-1][j], dp[i][j-1])

        lcs_length = dp[m][n]
        max_length = max(m, n)

        return lcs_length / max_length if max_length > 0 else 0.0

    def _analyze_series(self, files: List[Dict]) -> Dict:
        """分析系列特征（增强版）"""
        names = [f['name'] for f in files]

        # 尝试所有模式，按优先级排序
        pattern_results = []

        for pattern_info in self.series_patterns:
            result = self._detect_pattern_by_regex(names, pattern_info)
            if result['confidence'] > 0.3:  # 最低置信度阈值
                pattern_results.append(result)

        # 按置信度排序，返回最佳匹配
        if pattern_results:
            best_result = max(pattern_results, key=lambda x: x['confidence'])
            return best_result

        # 如果没有找到好的模式，返回默认结果
        return {
            'name': self._extract_common_prefix(names),
            'pattern': 'unknown',
            'confidence': 0.3
        }

    def _detect_pattern_by_regex(self, names: List[str], pattern_info: Dict) -> Dict:
        """使用正则表达式检测模式

        Args:
            names: 文件名列表
            pattern_info: 模式信息字典

        Returns:
            Dict: 检测结果
        """
        matches = []
        regex = pattern_info['regex']

        for name in names:
            match = re.search(regex, name.lower(), re.IGNORECASE)
            if match:
                matches.append(match)

        if len(matches) >= 2:  # 至少需要2个匹配
            # 检查基础名称是否一致
            base_names = [match.group(1).strip() for match in matches]
            unique_bases = set(base_names)

            if len(unique_bases) == 1:
                # 基础名称一致，计算置信度
                base_confidence = pattern_info['priority']

                # 根据匹配数量调整置信度
                match_ratio = len(matches) / len(names)
                confidence = base_confidence * match_ratio + pattern_info['confidence_boost']

                # 检查序列的连续性
                continuity_bonus = self._check_sequence_continuity(matches, pattern_info)
                confidence += continuity_bonus

                return {
                    'name': base_names[0],
                    'pattern': pattern_info['name'],
                    'confidence': min(1.0, confidence),
                    'match_count': len(matches),
                    'total_files': len(names)
                }

        return {
            'name': '',
            'pattern': pattern_info['name'],
            'confidence': 0.0
        }

    def _check_sequence_continuity(self, matches: List, pattern_info: Dict) -> float:
        """检查序列的连续性

        Args:
            matches: 正则匹配结果列表
            pattern_info: 模式信息

        Returns:
            float: 连续性奖励分数
        """
        if len(matches) < 2:
            return 0.0

        pattern_name = pattern_info['name']

        try:
            if pattern_name in ['version_pattern', 'semantic_version', 'numbered_pattern']:
                # 数字序列连续性检查
                numbers = []
                for match in matches:
                    if len(match.groups()) >= 2:
                        numbers.append(int(match.group(2)))

                if len(numbers) >= 2:
                    numbers.sort()
                    gaps = [numbers[i+1] - numbers[i] for i in range(len(numbers)-1)]
                    avg_gap = sum(gaps) / len(gaps)

                    # 如果平均间隔接近1，给予奖励
                    if avg_gap <= 1.5:
                        return 0.1
                    elif avg_gap <= 3:
                        return 0.05

            elif pattern_name == 'alphabetic_series':
                # 字母序列连续性检查
                letters = []
                for match in matches:
                    if len(match.groups()) >= 2:
                        letters.append(ord(match.group(2).upper()))

                if len(letters) >= 2:
                    letters.sort()
                    gaps = [letters[i+1] - letters[i] for i in range(len(letters)-1)]
                    avg_gap = sum(gaps) / len(gaps)

                    # 如果平均间隔接近1，给予奖励
                    if avg_gap <= 1.5:
                        return 0.1

        except (ValueError, IndexError):
            pass

        return 0.0

    def _extract_common_prefix(self, names: List[str]) -> str:
        """提取文件名的公共前缀

        Args:
            names: 文件名列表

        Returns:
            str: 公共前缀
        """
        if not names:
            return "未知系列"

        if len(names) == 1:
            return names[0]

        # 找到最长公共前缀
        prefix = names[0]
        for name in names[1:]:
            while prefix and not name.startswith(prefix):
                prefix = prefix[:-1]

        # 清理前缀（移除末尾的分隔符）
        prefix = prefix.rstrip('_-. ')

        return prefix if prefix else "文件系列"



    def learn_from_user_action(self, action_data: Dict):
        """从用户行为中学习

        Args:
            action_data: 用户行为数据，包含文件信息和用户操作
        """
        try:
            # 提取模式键
            pattern_key = self._generate_pattern_key(action_data)

            if pattern_key not in self.learning_patterns:
                self.learning_patterns[pattern_key] = {
                    'frequency': 0,
                    'success_rate': 0.5,
                    'tag_preferences': defaultdict(int),
                    'last_updated': datetime.now().isoformat()
                }

            pattern = self.learning_patterns[pattern_key]
            pattern['frequency'] += 1
            pattern['last_updated'] = datetime.now().isoformat()

            # 更新标签偏好
            action_type = action_data.get('action_type', '')
            if action_type == 'tag_applied':
                for tag in action_data.get('applied_tags', []):
                    pattern['tag_preferences'][tag] += 1

            elif action_type == 'tag_rejected':
                for tag in action_data.get('rejected_tags', []):
                    if tag in pattern['tag_preferences']:
                        pattern['tag_preferences'][tag] = max(0, pattern['tag_preferences'][tag] - 1)

            # 保存学习数据到数据库
            if self.db:
                self._save_learning_pattern(pattern_key, pattern)

        except Exception as e:
            print(f"用户行为学习失败: {e}")

    def _generate_pattern_key(self, action_data: Dict) -> str:
        """生成模式键用于学习"""
        file_info = action_data.get('file_info', {})
        extension = file_info.get('extension', '').lower()

        # 基于文件扩展名和路径特征生成键
        path_parts = file_info.get('path', '').lower().split(os.sep)
        path_keywords = []

        for part in path_parts:
            if any(keyword in part for keyword in ['work', 'project', 'personal', 'download']):
                path_keywords.append(part)

        pattern_key = f"{extension}_{'-'.join(path_keywords[:2])}"
        return pattern_key

    def _load_learning_patterns(self):
        """从数据库加载学习模式"""
        try:
            if not self.db:
                return

            cursor = self.db.conn.cursor()
            cursor.execute("""
                SELECT pattern_type, pattern_data, frequency, success_rate, last_used
                FROM ai_learning_patterns
                WHERE pattern_type = 'tagging'
            """)

            rows = cursor.fetchall()
            for row in rows:
                pattern_type, pattern_data_str, frequency, success_rate, last_used = row

                try:
                    pattern_data = json.loads(pattern_data_str)
                    pattern_key = pattern_data.get('pattern_key', '')

                    if pattern_key:
                        self.learning_patterns[pattern_key] = {
                            'frequency': frequency,
                            'success_rate': success_rate,
                            'tag_preferences': defaultdict(int, pattern_data.get('tag_preferences', {})),
                            'last_updated': last_used
                        }

                except json.JSONDecodeError as e:
                    print(f"解析学习模式数据失败: {e}")

        except Exception as e:
            print(f"加载学习模式失败: {e}")

    def _save_learning_pattern(self, pattern_key: str, pattern: Dict):
        """保存学习模式到数据库"""
        try:
            if not self.db:
                return

            cursor = self.db.conn.cursor()

            # 准备数据
            pattern_data = {
                'pattern_key': pattern_key,
                'tag_preferences': dict(pattern['tag_preferences'])
            }
            pattern_data_str = json.dumps(pattern_data, ensure_ascii=False)

            # 使用UPSERT操作（INSERT OR REPLACE）
            cursor.execute("""
                INSERT OR REPLACE INTO ai_learning_patterns
                (id, pattern_type, pattern_data, frequency, success_rate, last_used, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                f"tagging_{pattern_key}",
                'tagging',
                pattern_data_str,
                pattern['frequency'],
                pattern['success_rate'],
                pattern['last_updated'],
                pattern.get('created_at', pattern['last_updated'])
            ))

            self.db.conn.commit()

        except Exception as e:
            print(f"保存学习模式失败: {e}")

    def get_learning_statistics(self) -> Dict:
        """获取学习统计信息"""
        stats = {
            'total_patterns': len(self.learning_patterns),
            'active_patterns': 0,
            'top_patterns': [],
            'tag_usage': defaultdict(int)
        }

        # 统计活跃模式（最近使用过的）
        recent_threshold = datetime.now().timestamp() - (30 * 24 * 3600)  # 30天前

        pattern_scores = []
        for pattern_key, pattern in self.learning_patterns.items():
            try:
                last_updated = datetime.fromisoformat(pattern['last_updated']).timestamp()
                if last_updated > recent_threshold:
                    stats['active_patterns'] += 1

                # 计算模式评分（频率 * 成功率）
                score = pattern['frequency'] * pattern['success_rate']
                pattern_scores.append((pattern_key, score, pattern))

                # 统计标签使用情况
                for tag, count in pattern['tag_preferences'].items():
                    stats['tag_usage'][tag] += count

            except (ValueError, TypeError):
                continue

        # 获取前5个最佳模式
        pattern_scores.sort(key=lambda x: x[1], reverse=True)
        stats['top_patterns'] = [
            {
                'pattern_key': key,
                'score': score,
                'frequency': pattern['frequency'],
                'success_rate': pattern['success_rate']
            }
            for key, score, pattern in pattern_scores[:5]
        ]

        return stats
