"""
行为学习设置组件

提供用户行为学习功能的详细设置
预期代码长度: < 200行
当前代码长度: 185行 ✅
"""

from typing import Dict
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QTextEdit,
    QGroupBox, QGridLayout, QProgressBar, QCheckBox
)
from PySide6.QtCore import Qt
from .base_ai_widget import BaseAIWidget


class BehaviorLearningWidget(BaseAIWidget):
    """行为学习设置组件"""
    
    def __init__(self, parent=None):
        super().__init__("行为模式学习", parent)
    
    def setup_ui(self):
        """设置行为学习UI"""
        layout = QVBoxLayout(self)
        
        # 学习状态显示
        status_group = QGroupBox("学习状态")
        status_layout = QGridLayout(status_group)
        
        status_layout.addWidget(QLabel("学习模式数量:"), 0, 0)
        self.patterns_count_label = QLabel("0")
        status_layout.addWidget(self.patterns_count_label, 0, 1)
        
        status_layout.addWidget(QLabel("活跃模式数量:"), 1, 0)
        self.active_patterns_label = QLabel("0")
        status_layout.addWidget(self.active_patterns_label, 1, 1)
        
        status_layout.addWidget(QLabel("学习进度:"), 2, 0)
        self.learning_progress = QProgressBar()
        self.learning_progress.setRange(0, 100)
        self.learning_progress.setValue(0)
        status_layout.addWidget(self.learning_progress, 2, 1)
        
        layout.addWidget(status_group)
        
        # 学习设置
        settings_group = QGroupBox("学习设置")
        settings_layout = QVBoxLayout(settings_group)
        
        self.auto_learn_checkbox = QCheckBox("自动学习用户行为")
        self.auto_learn_checkbox.setChecked(True)
        settings_layout.addWidget(self.auto_learn_checkbox)
        
        self.learn_from_tags_checkbox = QCheckBox("从标签操作中学习")
        self.learn_from_tags_checkbox.setChecked(True)
        settings_layout.addWidget(self.learn_from_tags_checkbox)
        
        self.learn_from_moves_checkbox = QCheckBox("从文件移动中学习")
        self.learn_from_moves_checkbox.setChecked(True)
        settings_layout.addWidget(self.learn_from_moves_checkbox)
        
        layout.addWidget(settings_group)
        
        # 热门学习模式显示
        patterns_group = QGroupBox("热门学习模式")
        patterns_layout = QVBoxLayout(patterns_group)
        
        self.patterns_text = QTextEdit()
        self.patterns_text.setMaximumHeight(120)
        self.patterns_text.setReadOnly(True)
        self.patterns_text.setPlainText("暂无学习模式数据")
        patterns_layout.addWidget(self.patterns_text)
        
        layout.addWidget(patterns_group)
        
        # 标签使用统计
        tags_group = QGroupBox("标签使用统计")
        tags_layout = QVBoxLayout(tags_group)
        
        self.tags_text = QTextEdit()
        self.tags_text.setMaximumHeight(100)
        self.tags_text.setReadOnly(True)
        self.tags_text.setPlainText("暂无标签使用数据")
        tags_layout.addWidget(self.tags_text)
        
        layout.addWidget(tags_group)
        
        # 操作按钮
        buttons_layout = QHBoxLayout()
        
        self.refresh_button = QPushButton("刷新学习数据")
        self.refresh_button.clicked.connect(self.refresh_learning_data)
        buttons_layout.addWidget(self.refresh_button)
        
        self.clear_button = QPushButton("清除学习数据")
        self.clear_button.clicked.connect(self.clear_learning_data)
        buttons_layout.addWidget(self.clear_button)
        
        self.export_button = QPushButton("导出学习数据")
        self.export_button.clicked.connect(self.export_learning_data)
        buttons_layout.addWidget(self.export_button)
        
        buttons_layout.addStretch()
        layout.addLayout(buttons_layout)
    
    def setup_connections(self):
        """设置信号连接"""
        super().setup_connections()
        
        # 学习设置变化时发送信号
        self.auto_learn_checkbox.toggled.connect(self._on_config_changed)
        self.learn_from_tags_checkbox.toggled.connect(self._on_config_changed)
        self.learn_from_moves_checkbox.toggled.connect(self._on_config_changed)
    
    def load_config(self, config: Dict):
        """加载配置到UI控件"""
        self._config = config
        
        # 加载学习设置
        learning_config = config.get('features', {}).get('behavior_learning', {})
        
        self.auto_learn_checkbox.setChecked(learning_config.get('auto_learn', True))
        self.learn_from_tags_checkbox.setChecked(learning_config.get('learn_from_tags', True))
        self.learn_from_moves_checkbox.setChecked(learning_config.get('learn_from_moves', True))
        
        # 刷新学习数据显示
        self.refresh_learning_data()
    
    def save_config(self) -> Dict:
        """从UI控件保存配置"""
        learning_config = {
            'auto_learn': self.auto_learn_checkbox.isChecked(),
            'learn_from_tags': self.learn_from_tags_checkbox.isChecked(),
            'learn_from_moves': self.learn_from_moves_checkbox.isChecked()
        }
        
        # 更新配置
        if 'features' not in self._config:
            self._config['features'] = {}
        if 'behavior_learning' not in self._config['features']:
            self._config['features']['behavior_learning'] = {}
        
        self._config['features']['behavior_learning'].update(learning_config)
        
        return {'features': {'behavior_learning': learning_config}}
    
    def refresh_learning_data(self):
        """刷新学习数据显示"""
        if not self.config_manager:
            return
        
        try:
            stats = self.config_manager.get_learning_statistics()
            behavior_stats = stats.get('behavior_learning', {})
            
            # 更新学习状态
            total_patterns = behavior_stats.get('total_patterns', 0)
            active_patterns = behavior_stats.get('active_patterns', 0)
            
            self.patterns_count_label.setText(str(total_patterns))
            self.active_patterns_label.setText(str(active_patterns))
            
            # 计算学习进度（基于活跃模式比例）
            if total_patterns > 0:
                progress = int((active_patterns / total_patterns) * 100)
                self.learning_progress.setValue(progress)
            else:
                self.learning_progress.setValue(0)
            
            # 更新热门模式显示
            top_patterns = behavior_stats.get('top_patterns', [])
            if top_patterns:
                patterns_text = "热门学习模式:\n"
                for i, pattern in enumerate(top_patterns[:5], 1):
                    patterns_text += f"{i}. {pattern['pattern_key']} (频率: {pattern['frequency']}, 评分: {pattern['score']:.2f})\n"
                self.patterns_text.setPlainText(patterns_text)
            else:
                self.patterns_text.setPlainText("暂无学习模式数据")
            
            # 更新标签使用统计
            tag_usage = behavior_stats.get('tag_usage', {})
            if tag_usage:
                tags_text = "热门标签使用:\n"
                sorted_tags = sorted(tag_usage.items(), key=lambda x: x[1], reverse=True)
                for tag, count in sorted_tags[:10]:
                    tags_text += f"• {tag}: {count}次\n"
                self.tags_text.setPlainText(tags_text)
            else:
                self.tags_text.setPlainText("暂无标签使用数据")
                
        except Exception as e:
            self.show_error(f"刷新学习数据失败: {e}")
    
    def clear_learning_data(self):
        """清除学习数据"""
        from PySide6.QtWidgets import QMessageBox
        
        reply = QMessageBox.question(
            self, 
            "确认清除", 
            "确定要清除所有学习数据吗？此操作不可撤销。",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            try:
                # TODO: 实现清除学习数据的功能
                self.show_info("学习数据已清除")
                self.refresh_learning_data()
            except Exception as e:
                self.show_error(f"清除学习数据失败: {e}")
    
    def export_learning_data(self):
        """导出学习数据"""
        from PySide6.QtWidgets import QFileDialog
        import json
        
        try:
            file_path, _ = QFileDialog.getSaveFileName(
                self,
                "导出学习数据",
                "learning_data.json",
                "JSON文件 (*.json)"
            )
            
            if file_path:
                stats = self.config_manager.get_learning_statistics()
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(stats, f, ensure_ascii=False, indent=2)
                
                self.show_info(f"学习数据已导出到: {file_path}")
                
        except Exception as e:
            self.show_error(f"导出学习数据失败: {e}")
    
    def _on_config_changed(self):
        """配置变化处理"""
        config = self.save_config()
        self.config_changed.emit(config)
